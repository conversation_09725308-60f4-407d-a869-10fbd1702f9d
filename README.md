# XWiki Watermark Extension

A professional watermark extension for XWiki 17.4.3 that provides dynamic text watermarks with placeholder support, anti-copy protection, and mobile compatibility.

## Features

- **Dynamic Watermark Rendering**: High-quality Canvas-based watermark generation
- **Placeholder Support**: Supports `${user}` and `${timestamp}` placeholders with automatic replacement  
- **Flexible Configuration**: Configurable watermark text, style, position, transparency, and other parameters
- **Anti-Copy Protection**: Optional content protection features to prevent selection and copying
- **Mobile Adaptation**: Responsive design with mobile device support
- **Multi-language Support**: Automatic switching between Chinese/English
- **XWiki Standard Integration**: Integrated into XWiki's standard management interface

## Installation

1. Build the extension:
   ```bash
   mvn clean package
   ```

2. Upload the generated XAR file (`target/xwiki-watermark-extension-1.0-SNAPSHOT.xar`) to your XWiki instance through the Extension Manager.

3. The extension will be automatically installed and ready to use.

## Configuration

1. Access `http://localhost:8080/bin/admin/XWiki/XWikiPreferences`
2. In the left navigation, find **"Watermark"** under the **"Look & Feel"** category
3. Check the **"Enable Watermark"** option
4. Adjust configuration parameters as needed
5. Click **"Save Configuration"** button

### Configuration Parameters

| Parameter | Type | Description | Default Value |
|-----------|------|-------------|---------------|
| enabled | boolean | Enable or disable watermark | false |
| textTemplate | string | Watermark text template | "${user} - ${timestamp}" |
| xSpacing | integer | Horizontal spacing (pixels) | 200 |
| ySpacing | integer | Vertical spacing (pixels) | 100 |
| angle | integer | Rotation angle (degrees) | -30 |
| opacity | float | Transparency (0.0-1.0) | 0.1 |
| fontSize | integer | Font size (pixels) | 14 |
| antiCopy | boolean | Enable anti-copy protection | false |
| applyToMobile | boolean | Apply to mobile devices | true |

## Architecture

This extension uses a simplified architecture with only 5 core files:

- **WatermarkConfigClass.xml**: Configuration class definition
- **WatermarkAdmin.xml**: Management interface (integrated into Look & Feel)
- **WatermarkSkinExtension.xml**: JavaScript implementation
- **WatermarkStyleExtension.xml**: CSS styles
- **Translation files**: Multi-language support

## Technical Requirements

- XWiki 17.x
- Java 17.x  
- Maven 3.x

## Development

This extension follows XWiki's recommended practices:
- Uses XAR packaging format for easy installation without XWiki restarts
- Follows "function first, code simplicity" principle
- Minimal architecture with clear separation of concerns

## License

This software is licensed under the GNU Lesser General Public License (LGPL) version 2.1 or later.
