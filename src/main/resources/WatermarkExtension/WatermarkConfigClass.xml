<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkConfigClass" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkConfigClass</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Configuration Class</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <content>{{include reference="XWiki.ClassSheet"/}}</content>
  <class>
    <name>WatermarkExtension.WatermarkConfigClass</name>
    <customClass/>
    <customMapping/>
    <defaultViewSheet/>
    <defaultEditSheet/>
    <defaultWeb/>
    <nameField/>
    <validationScript/>
    <enabled>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      <customDisplay/>
      <defaultValue>0</defaultValue>
      <displayFormType>checkbox</displayFormType>
      <displayType>yesno</displayType>
      <name>enabled</name>
      <number>1</number>
      <prettyName>WatermarkExtension.WatermarkConfigClass_enabled</prettyName>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </enabled>
    <textTemplate>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.TextAreaClass</classType>
      <customDisplay/>
      <defaultValue>${user} - ${timestamp}</defaultValue>
      <editor>Text</editor>
      <name>textTemplate</name>
      <number>2</number>
      <prettyName>WatermarkExtension.WatermarkConfigClass_textTemplate</prettyName>
      <rows>3</rows>
      <size>60</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </textTemplate>
    <xSpacing>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>200</defaultValue>
      <name>xSpacing</name>
      <number>3</number>
      <numberType>integer</numberType>
      <prettyName>WatermarkExtension.WatermarkConfigClass_xSpacing</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>watermark.validation.xSpacing</validationMessage>
      <validationRegExp>^([5-9][0-9]|[1-4][0-9][0-9]|500)$</validationRegExp>
    </xSpacing>
    <ySpacing>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>100</defaultValue>
      <name>ySpacing</name>
      <number>4</number>
      <numberType>integer</numberType>
      <prettyName>WatermarkExtension.WatermarkConfigClass_ySpacing</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>watermark.validation.ySpacing</validationMessage>
      <validationRegExp>^([5-9][0-9]|[1-4][0-9][0-9]|500)$</validationRegExp>
    </ySpacing>
    <angle>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>-30</defaultValue>
      <name>angle</name>
      <number>5</number>
      <numberType>integer</numberType>
      <prettyName>WatermarkExtension.WatermarkConfigClass_angle</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>watermark.validation.angle</validationMessage>
      <validationRegExp>^-?(1[0-7][0-9]|180|[1-9]?[0-9])$</validationRegExp>
    </angle>
    <opacity>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>0.3</defaultValue>
      <name>opacity</name>
      <number>6</number>
      <numberType>float</numberType>
      <prettyName>WatermarkExtension.WatermarkConfigClass_opacity</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>watermark.validation.opacity</validationMessage>
      <validationRegExp>^(0(\.[0-9]+)?|1(\.0+)?)$</validationRegExp>
    </opacity>
    <fontSize>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.NumberClass</classType>
      <customDisplay/>
      <defaultValue>14</defaultValue>
      <name>fontSize</name>
      <number>7</number>
      <numberType>integer</numberType>
      <prettyName>WatermarkExtension.WatermarkConfigClass_fontSize</prettyName>
      <size>10</size>
      <unmodifiable>0</unmodifiable>
      <validationMessage>watermark.validation.fontSize</validationMessage>
      <validationRegExp>^([8-9]|[1-3][0-9]|4[0-8])$</validationRegExp>
    </fontSize>
    <antiCopy>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      <customDisplay/>
      <defaultValue>0</defaultValue>
      <displayFormType>checkbox</displayFormType>
      <displayType>yesno</displayType>
      <name>antiCopy</name>
      <number>8</number>
      <prettyName>WatermarkExtension.WatermarkConfigClass_antiCopy</prettyName>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </antiCopy>
    <applyToMobile>
      <cache>0</cache>
      <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      <customDisplay/>
      <defaultValue>1</defaultValue>
      <displayFormType>checkbox</displayFormType>
      <displayType>yesno</displayType>
      <name>applyToMobile</name>
      <number>9</number>
      <prettyName>WatermarkExtension.WatermarkConfigClass_applyToMobile</prettyName>
      <unmodifiable>0</unmodifiable>
      <validationMessage/>
      <validationRegExp/>
    </applyToMobile>
  </class>
</xwikidoc>
