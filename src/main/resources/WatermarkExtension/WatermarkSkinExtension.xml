<?xml version="1.1" encoding="UTF-8"?>
<xwikidoc version="1.5" reference="WatermarkExtension.WatermarkSkinExtension" locale="">
  <web>WatermarkExtension</web>
  <name>WatermarkSkinExtension</name>
  <language/>
  <defaultLanguage/>
  <translation>0</translation>
  <creator>xwiki:XWiki.Admin</creator>
  <parent>WatermarkExtension.WebHome</parent>
  <author>xwiki:XWiki.Admin</author>
  <contentAuthor>xwiki:XWiki.Admin</contentAuthor>
  <version>1.1</version>
  <title>Watermark Skin Extension</title>
  <comment/>
  <minorEdit>false</minorEdit>
  <syntaxId>xwiki/2.1</syntaxId>
  <hidden>true</hidden>
  <object>
    <name>WatermarkExtension.WatermarkSkinExtension</name>
    <number>0</number>
    <className>XWiki.JavaScriptExtension</className>
    <guid>watermark-skin-extension</guid>
    <class>
      <name>XWiki.JavaScriptExtension</name>
      <customClass/>
      <customMapping/>
      <defaultViewSheet/>
      <defaultEditSheet/>
      <defaultWeb/>
      <nameField/>
      <validationScript/>
      <cache>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>cache</name>
        <number>5</number>
        <prettyName>Caching policy</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>long|short|default|forbid</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </cache>
      <code>
        <contenttype>PureText</contenttype>
        <disabled>0</disabled>
        <editor>PureText</editor>
        <name>code</name>
        <number>2</number>
        <prettyName>Code</prettyName>
        <rows>20</rows>
        <size>50</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.TextAreaClass</classType>
      </code>
      <name>
        <disabled>0</disabled>
        <name>name</name>
        <number>1</number>
        <prettyName>Name</prettyName>
        <size>30</size>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.StringClass</classType>
      </name>
      <parse>
        <disabled>0</disabled>
        <displayFormType>select</displayFormType>
        <displayType>yesno</displayType>
        <name>parse</name>
        <number>4</number>
        <prettyName>Parse content</prettyName>
        <unmodifiable>0</unmodifiable>
        <classType>com.xpn.xwiki.objects.classes.BooleanClass</classType>
      </parse>
      <use>
        <cache>0</cache>
        <disabled>0</disabled>
        <displayType>select</displayType>
        <multiSelect>0</multiSelect>
        <name>use</name>
        <number>3</number>
        <prettyName>Use this extension</prettyName>
        <relationalStorage>0</relationalStorage>
        <separator> </separator>
        <separators>|, </separators>
        <size>1</size>
        <unmodifiable>0</unmodifiable>
        <values>currentPage|onDemand|always</values>
        <classType>com.xpn.xwiki.objects.classes.StaticListClass</classType>
      </use>
    </class>
    <property>
      <cache>forbid</cache>
    </property>
    <property>
      <code><![CDATA[
## ---------------- Velocity Start (parsed because parse=1) ----------------
#if("$!xcontext.user" == "")
  #set($wmUserRef = "XWikiGuest")
#else
  #set($wmUserRef = "$xcontext.user")
#end
#set($wmUserPretty = $xwiki.getUserName($wmUserRef, false))  ## false=纯文本

/* 服务端注入的上下文，前端直接读取 */
window.XWikiWatermarkCtx = {
  userRef: "$escapetool.javascript($wmUserRef)",      // 用户引用
  user: "$escapetool.javascript($wmUserPretty)",      // 展示名
  isGuest: #if($wmUserRef == "XWikiGuest") true #else false #end
};
## ---------------- Velocity End ----------------


/* XWiki Watermark Extension - JavaScript Implementation */
(function() {
  'use strict';

  // XWiki Watermark Engine
  window.XWikiWatermarkEngine = {
    config: {
      enabled: false,  // Will be overridden by loaded config
      textTemplate: '${user} - ${timestamp}',
      xSpacing: 200,
      ySpacing: 100,
      angle: -30,
      opacity: 0.1,
      fontSize: 14,
      antiCopy: false,
      applyToMobile: true
    },
    
    canvas: null,
    ctx: null,
    
    // Initialize watermark system
    init: function() {
      this.loadConfig();
    },
    
    // Load configuration from WatermarkConfiguration or XWikiPreferences
    loadConfig: function() {
      var self = this;

      // Try to get config from global variable first (set by admin page)
      if (window.watermarkConfig) {
        console.log('Using global watermark config:', window.watermarkConfig);
        this.updateConfig(window.watermarkConfig);
        return;
      }

      console.log('Loading watermark config via REST API...');

      // Try WatermarkConfiguration page first (priority 1)
      this.loadConfigFromPage('WatermarkExtension', 'WatermarkConfiguration', function(success) {
        if (!success) {
          // Fallback to XWikiPreferences (priority 2) for backward compatibility
          console.log('WatermarkConfiguration not found, trying XWikiPreferences...');
          self.loadConfigFromPage('XWiki', 'XWikiPreferences', function(success) {
            if (!success) {
              // Try alternative script method if REST API fails
              console.log('XWikiPreferences access failed, trying alternative method...');
              self.loadConfigViaScript();
            }
          });
        }
      });
    },

    // Load configuration from a specific page
    loadConfigFromPage: function(space, page, callback) {
      var self = this;
      var url = '/rest/wikis/xwiki/spaces/' + space + '/pages/' + page + '/objects/WatermarkExtension.WatermarkConfigClass';

      console.log('Trying to load config from:', url);

      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.setRequestHeader('Accept', 'application/json');
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            try {
              var data = JSON.parse(xhr.responseText);
              console.log('REST API response from ' + space + '.' + page + ':', data);
              if (data.objectSummaries && data.objectSummaries.length > 0) {
                console.log('Found config object in ' + space + '.' + page + ', loading details...');
                self.getConfigObjectFromPage(data.objectSummaries[0], space, page);
                callback(true);
                return;
              } else {
                console.log('No config objects found in ' + space + '.' + page);
                callback(false);
                return;
              }
            } catch (e) {
              console.warn('Failed to parse REST API response from ' + space + '.' + page + ':', e);
              callback(false);
              return;
            }
          } else if (xhr.status === 401) {
            console.log('REST API access denied (401) for ' + space + '.' + page);
            callback(false);
            return;
          } else {
            console.warn('REST API request failed for ' + space + '.' + page + ' with status:', xhr.status);
            callback(false);
            return;
          }
        }
      };
      xhr.send();
    },

    // Alternative method to load config via script tag (works for anonymous users)
    loadConfigViaScript: function() {
      var self = this;
      console.log('Loading config via script method...');

      // Create a script tag to load config from a public endpoint
      var script = document.createElement('script');
      script.src = '/bin/get/WatermarkExtension/WatermarkConfigProvider?outputSyntax=plain&xpage=plain';
      script.onload = function() {
        if (window.xwikiWatermarkPublicConfig) {
          console.log('Config loaded via script:', window.xwikiWatermarkPublicConfig);
          self.updateConfig(window.xwikiWatermarkPublicConfig);
        } else {
          console.log('No public config found, using defaults');
          self.updateConfig(self.config);
        }
      };
      script.onerror = function() {
        console.log('Failed to load config via script, using defaults');
        self.updateConfig(self.config);
      };
      document.head.appendChild(script);
    },

    // Get detailed config object from a specific page
    getConfigObjectFromPage: function(objectSummary, space, page) {
      var self = this;
      // Use the number property instead of the full ID
      var objectNumber = objectSummary.number || 0;
      var url = '/rest/wikis/xwiki/spaces/' + space + '/pages/' + page + '/objects/WatermarkExtension.WatermarkConfigClass/' + objectNumber;
      console.log('Loading config object from:', url);

      var xhr = new XMLHttpRequest();
      xhr.open('GET', url, true);
      xhr.setRequestHeader('Accept', 'application/json');
      xhr.onreadystatechange = function() {
        if (xhr.readyState === 4) {
          if (xhr.status === 200) {
            try {
              var obj = JSON.parse(xhr.responseText);
              console.log('Config object loaded from ' + space + '.' + page + ':', obj);

              // Convert properties array to object for easier access
              var props = {};
              if (obj.properties && Array.isArray(obj.properties)) {
                obj.properties.forEach(function(prop) {
                  props[prop.name] = prop.value;
                });
              }
              console.log('Properties parsed:', props);

              var config = {
                enabled: props.enabled === '1' || props.enabled === 1,
                textTemplate: props.textTemplate || '${user} - ${timestamp}',
                xSpacing: parseInt(props.xSpacing) || 200,
                ySpacing: parseInt(props.ySpacing) || 100,
                angle: parseInt(props.angle) || -30,
                opacity: parseFloat(props.opacity) || 0.1,
                fontSize: parseInt(props.fontSize) || 14,
                antiCopy: props.antiCopy === '1' || props.antiCopy === 1,
                applyToMobile: props.applyToMobile === '1' || props.applyToMobile === 1
              };
              console.log('Parsed config from ' + space + '.' + page + ':', config);
              self.updateConfig(config);
            } catch (e) {
              console.error('Failed to parse watermark config from ' + space + '.' + page + ':', e);
              self.updateConfig(self.config);
            }
          } else {
            console.error('Failed to load watermark config object from ' + space + '.' + page + ', status:', xhr.status, 'URL:', url);
            self.updateConfig(self.config);
          }
        }
      };
      xhr.send();
    },

    // Get detailed config object (backward compatibility)
    getConfigObject: function(objectSummary) {
      this.getConfigObjectFromPage(objectSummary, 'XWiki', 'XWikiPreferences');
    },

    // Update configuration and apply watermark
    updateConfig: function(newConfig) {
      this.config = Object.assign(this.config, newConfig);
      console.log('Watermark config updated:', this.config);

      if (this.config.enabled) {
        console.log('Watermark is enabled, creating watermark...');
        this.createWatermark();
        if (this.config.antiCopy) {
          this.enableAntiCopy();
        }
      } else {
        console.log('Watermark is disabled');
        this.removeWatermark();
        this.disableAntiCopy();
      }
    },

    // Create watermark canvas
    createWatermark: function() {
      // Check mobile device
      var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile && !this.config.applyToMobile) {
        return;
      }

      // Remove existing watermark
      this.removeWatermark();

      // Create canvas
      this.canvas = document.createElement('canvas');
      this.canvas.className = 'watermark-canvas';
      this.canvas.style.opacity = this.config.opacity;

      // Set canvas size
      this.canvas.width = window.innerWidth;
      this.canvas.height = window.innerHeight;

      // Get context
      this.ctx = this.canvas.getContext('2d');

      // Draw watermark
      this.drawWatermark();

      // Add to page
      document.body.appendChild(this.canvas);

      // Handle window resize
      var self = this;
      window.addEventListener('resize', function() {
        self.canvas.width = window.innerWidth;
        self.canvas.height = window.innerHeight;
        self.drawWatermark();
      });
    },

    // Draw watermark on canvas
    drawWatermark: function() {
      if (!this.ctx) return;

      // Clear canvas
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // Get processed text with placeholders replaced
      var text = this.processPlaceholders(this.config.textTemplate);

      // Set text properties
      this.ctx.font = this.config.fontSize + 'px Arial, sans-serif';
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
      this.ctx.textAlign = 'center';
      this.ctx.textBaseline = 'middle';

      // Draw watermark grid
      for (var x = 0; x < this.canvas.width + this.config.xSpacing; x += this.config.xSpacing) {
        for (var y = 0; y < this.canvas.height + this.config.ySpacing; y += this.config.ySpacing) {
          this.ctx.save();

          // Move to position and rotate
          this.ctx.translate(x, y);
          this.ctx.rotate((this.config.angle * Math.PI) / 180);

          // Draw text
          this.ctx.fillText(text, 0, 0);

          this.ctx.restore();
        }
      }
    },

    // Process placeholder variables
    processPlaceholders: function(template) {
      var text = template;

      // Replace ${user} with current user
      var currentUser = (window.XWikiWatermarkCtx && window.XWikiWatermarkCtx.user) || 'Guest';
      text = text.replace(/\$\{user\}/g, currentUser);

      // Replace ${timestamp} with current time
      text = text.replace(/\$\{timestamp\}/g, new Date().toLocaleString());

      return text;
    },

    // Remove watermark
    removeWatermark: function() {
      if (this.canvas && this.canvas.parentNode) {
        this.canvas.parentNode.removeChild(this.canvas);
      }
      this.canvas = null;
      this.ctx = null;
    },

    // Enable anti-copy protection
    enableAntiCopy: function() {
      document.body.classList.add('watermark-anticopy');

      // Disable right-click context menu
      document.addEventListener('contextmenu', this.preventContextMenu);

      // Disable text selection shortcuts
      document.addEventListener('keydown', this.preventKeyboardShortcuts);

      // Disable drag and drop
      document.addEventListener('dragstart', this.preventDragStart);
    },

    // Disable anti-copy protection
    disableAntiCopy: function() {
      document.body.classList.remove('watermark-anticopy');

      document.removeEventListener('contextmenu', this.preventContextMenu);
      document.removeEventListener('keydown', this.preventKeyboardShortcuts);
      document.removeEventListener('dragstart', this.preventDragStart);
    },

    // Prevent context menu
    preventContextMenu: function(e) {
      e.preventDefault();
      return false;
    },

    // Prevent keyboard shortcuts
    preventKeyboardShortcuts: function(e) {
      // Disable Ctrl+A, Ctrl+C, Ctrl+V, Ctrl+X, Ctrl+S, F12
      if (e.ctrlKey && (e.keyCode === 65 || e.keyCode === 67 || e.keyCode === 86 || e.keyCode === 88 || e.keyCode === 83)) {
        e.preventDefault();
        return false;
      }
      // Disable F12 (Developer Tools)
      if (e.keyCode === 123) {
        e.preventDefault();
        return false;
      }
    },

    // Prevent drag start
    preventDragStart: function(e) {
      e.preventDefault();
      return false;
    }
  };

  // Initialize watermark when DOM is ready
  function initWatermark() {
    if (window.XWikiWatermarkEngine) {
      window.XWikiWatermarkEngine.init();
    }
  }

  // Initialize
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initWatermark);
  } else {
    initWatermark();
  }
})();

]]></code>
    </property>
    <property>
      <name>WatermarkEngine</name>
    </property>
    <property>
      <parse>1</parse>
    </property>
    <property>
      <use>always</use>
    </property>
  </object>
  <content></content>
</xwikidoc>
